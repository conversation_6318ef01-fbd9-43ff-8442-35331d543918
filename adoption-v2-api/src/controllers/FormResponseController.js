const FormResponse = require("../models/FormResponse");
const UserModel = require("../models/UserModel");
const { validationResult } = require("express-validator");
const { httpResponse, sendEmail } = require("../utils/helpers");
const { HTTP_CODES } = require("../utils/enum");
const mongoose = require("mongoose");
const path = require("path");
const fs = require("fs");

// Özel bir e-posta gönderme fonksiyonu oluşturun ve doğrudan Azure API'yi kullanın
const sendFeedbackEmail = async (to, userName) => {
  try {
    const templatePath = path.join(
      __dirname,
      "../utils/emailTemplates/feedbackMail.html"
    );
    let emailTemplate = fs.readFileSync(templatePath, "utf8");

    emailTemplate = emailTemplate.replace(
      "${response[0]?.name}",
      userName || "User"
    );

    // Azure API'ye doğrudan erişim
    const { EmailClient } = require("@azure/communication-email");
    const connectionString = process.env.AZURE_EMAIL_CONNECTION_STRING;
    const emailClient = new EmailClient(connectionString);

    const emailMessage = {
      senderAddress: "<EMAIL>",
      content: {
        subject: "Your Support Request Has Been Received",
        html: emailTemplate,
      },
      recipients: {
        to: [
          {
            address: to,
          },
        ],
      },
    };

    const poller = await emailClient.beginSend(emailMessage);
    console.log(`Email sent successfully to ${to}`);
  } catch (error) {
    console.error("Error sending feedback email:", error);
  }
};

exports.create = async (req, res) => {
  try {
    if (!req.user || !req.user.email) {
      return httpResponse(
        res,
        HTTP_CODES.UNAUTHORIZED,
        "error",
        "User not authenticated"
      );
    }

    const user = await UserModel.findOne({ email: req.user.email });
    console.log("user:", user);
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Validation error",
        errors.array()
      );
    }

    const { formId, formType, title, description, responses } = req.body;

    console.log("object", req.body);

    if (formType === "help") {
      try {
        await sendFeedbackEmail(user.email, user.name);
        console.log(
          `Support request confirmation email sent to: ${user?.email}`
        );
      } catch (error) {
        console.error("Error sending email:", error);
      }
    }

    const formResponse = new FormResponse({
      formId,
      formType,
      title,
      description,
      responses,
      submittedBy: user._id,
      status: "draft",
    });

    await formResponse.save();

    return httpResponse(
      res,
      HTTP_CODES.CREATED,
      "success",
      "Form response created successfully",
      formResponse
    );
  } catch (error) {
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Tüm form yanıtlarını listeleme
exports.list = async (req, res) => {
  try {
    const {
      formId,
      status,
      formType,
      submittedBy,
      page = 1,
      limit = 10,
    } = req.query;

    const query = {};
    if (formId) query.formId = formId;
    if (status) query.status = status;
    if (formType) query.formType = formType;
    // Get user by email
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    // If is not admin, only show the user's form responses
    if (user.admin !== true) {
      query.submittedBy = user._id;
    } else if (submittedBy) {
      // If is admin and submittedBy is not null, show the submittedBy's form responses
      query.submittedBy = submittedBy;
    }

    let result;
    if (limit === -1) {
      // If limit is -1, get all records
      const docs = await FormResponse.find(query)
        .sort({ createdAt: -1 })
        .populate("submittedBy", "name email role");

      result = {
        docs,
        totalDocs: docs.length,
        limit: -1,
        totalPages: 1,
        page: 1,
        pagingCounter: 1,
        hasPrevPage: false,
        hasNextPage: false,
        prevPage: null,
        nextPage: null,
      };
    } else {
      // Manuel pagination
      const skip = (parseInt(page) - 1) * parseInt(limit);
      const [docs, totalDocs] = await Promise.all([
        FormResponse.find(query)
          .sort({ createdAt: -1 })
          .skip(skip)
          .limit(parseInt(limit))
          .populate("submittedBy", "name email role"),
        FormResponse.countDocuments(query),
      ]);

      const totalPages = Math.ceil(totalDocs / parseInt(limit));

      result = {
        docs,
        totalDocs,
        limit: parseInt(limit),
        totalPages,
        page: parseInt(page),
        pagingCounter: skip + 1,
        hasPrevPage: parseInt(page) > 1,
        hasNextPage: parseInt(page) < totalPages,
        prevPage: parseInt(page) > 1 ? parseInt(page) - 1 : null,
        nextPage: parseInt(page) < totalPages ? parseInt(page) + 1 : null,
      };
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form responses retrieved successfully",
      result
    );
  } catch (error) {
    console.error("Error in list form responses:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Belirli bir form yanıtını görüntüleme
exports.get = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId kontrolü
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    const formResponse = await FormResponse.findById(id).populate(
      "submittedBy",
      "name email"
    );

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response retrieved successfully",
      formResponse
    );
  } catch (error) {
    console.error("Error in get form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Form yanıtını güncelleme
exports.update = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId kontrolü
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    const { responses, status } = req.body;

    // Email'e göre user'ı bulalım
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const formResponse = await FormResponse.findById(id);

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    // Sadece yanıtı oluşturan kullanıcı güncelleyebilir
    if (formResponse.submittedBy.toString() !== user._id.toString()) {
      return httpResponse(
        res,
        HTTP_CODES.FORBIDDEN,
        "error",
        "You don't have permission to update this form response"
      );
    }

    if (responses) formResponse.responses = responses;
    if (status) formResponse.status = status;

    await formResponse.save();

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response updated successfully",
      formResponse
    );
  } catch (error) {
    console.error("Error in update form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};

// Form yanıtını silme
exports.delete = async (req, res) => {
  try {
    const { id } = req.params;

    // ObjectId kontrolü
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return httpResponse(
        res,
        HTTP_CODES.BAD_REQUEST,
        "error",
        "Invalid form response ID format"
      );
    }

    // Email'e göre user'ı bulalım
    const user = await UserModel.findOne({ email: req.user.email });
    if (!user) {
      return httpResponse(res, HTTP_CODES.NOT_FOUND, "error", "User not found");
    }

    const formResponse = await FormResponse.findById(id);

    if (!formResponse) {
      return httpResponse(
        res,
        HTTP_CODES.NOT_FOUND,
        "error",
        "Form response not found"
      );
    }

    // Sadece yanıtı oluşturan kullanıcı silebilir
    if (formResponse.submittedBy.toString() !== user._id.toString()) {
      return httpResponse(
        res,
        HTTP_CODES.FORBIDDEN,
        "error",
        "You don't have permission to delete this form response"
      );
    }

    await FormResponse.deleteOne({ _id: id });

    return httpResponse(
      res,
      HTTP_CODES.OK,
      "success",
      "Form response deleted successfully"
    );
  } catch (error) {
    console.error("Error in delete form response:", error);
    return httpResponse(
      res,
      HTTP_CODES.INT_SERVER_ERROR,
      "error",
      "An internal server error occurred",
      error.message
    );
  }
};
